# Backtest Framework Configuration

# 交易所设置
exchange = "Binance"

# 回测时间范围
start_time = "2025-07-07T11:23:20Z"
end_time = "2025-07-07T11:25:00Z"

# 数据路径配置
[data_paths]
# 默认数据根目录
root = "./data"

# 可选：为不同数据类型指定专门的路径
# 如果不指定，将使用root路径
bookticker = "./data/bookticker" # BookTicker数据路径
depth = "./data/depth"           # 深度数据路径
orderbook = "./data/orderbook"   # 订单簿数据路径
trades = "./data/trades"         # 交易数据路径

# 服务器端口配置
websocket_port = 8080
http_port = 8081

# 日志配置
log_level = "info"

# 性能配置
performance_target_us = 500
