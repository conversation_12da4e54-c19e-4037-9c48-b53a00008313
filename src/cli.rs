use crate::config::ConfigManager;
use crate::data::{DataStreamController, DataStreamStatus};
use crate::{BacktestFramework, Result};
use clap::{Parser, Subcommand};
use colored::*;
use rustyline::error::ReadlineError;
use rustyline::{DefaultEditor, Result as RustylineResult};
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{error, info};

/// 交互式命令行界面
#[derive(Parser)]
#[command(name = "backtest")]
#[command(about = "Backtest Framework Interactive CLI")]
pub struct Cli {
    #[command(subcommand)]
    pub command: Option<Commands>,
}

/// 支持的命令
#[derive(Subcommand)]
pub enum Commands {
    /// 启动数据流
    Start,
    /// 停止数据流
    Stop,
    /// 重启数据流
    Restart,
    /// 暂停数据流
    Pause,
    /// 恢复数据流
    Resume,
    /// 显示当前状态
    Status,
    /// 配置管理
    Config {
        #[command(subcommand)]
        action: ConfigAction,
    },
    /// 显示帮助信息
    Help,
    /// 退出程序
    Exit,
}

/// 配置相关的子命令
#[derive(Subcommand)]
pub enum ConfigAction {
    /// 显示当前配置
    Show,
    /// 设置配置文件（从TOML/JSON文件加载配置）
    Set {
        /// 配置文件路径
        path: String,
    },
    /// 验证配置
    Validate,
    /// 重置配置
    Reset,
    /// 从文件加载配置
    Load {
        /// 配置文件路径
        path: String,
    },
    /// 保存配置到文件
    Save {
        /// 配置文件路径
        path: String,
    },
}

/// 交互式CLI管理器
pub struct InteractiveCli {
    framework: Arc<BacktestFramework>,
    editor: DefaultEditor,
}

impl InteractiveCli {
    /// 创建新的交互式CLI
    pub fn new(framework: Arc<BacktestFramework>) -> RustylineResult<Self> {
        let editor = DefaultEditor::new()?;
        Ok(Self { framework, editor })
    }

    /// 启动交互式循环
    pub async fn run(&mut self) -> Result<()> {
        self.print_welcome();
        self.print_help();

        loop {
            let readline = self.editor.readline("backtest> ");
            match readline {
                Ok(line) => {
                    let line = line.trim();
                    if line.is_empty() {
                        continue;
                    }

                    // 添加到历史记录
                    let _ = self.editor.add_history_entry(line);

                    // 解析并执行命令
                    if let Err(e) = self.handle_command(line).await {
                        error!("{}: {}", "Error".red(), e);
                    }
                }
                Err(ReadlineError::Interrupted) => {
                    info!("{}", "Interrupted (Ctrl+C)".yellow());
                    break;
                }
                Err(ReadlineError::Eof) => {
                    info!("{}", "EOF (Ctrl+D)".yellow());
                    break;
                }
                Err(err) => {
                    error!("{}: {}", "Error".red(), err);
                    break;
                }
            }
        }

        Ok(())
    }

    /// 处理用户输入的命令
    async fn handle_command(&self, input: &str) -> Result<()> {
        let args: Vec<&str> = input.split_whitespace().collect();
        if args.is_empty() {
            return Ok(());
        }

        match args[0].to_lowercase().as_str() {
            "start" => self.handle_start().await,
            "stop" => self.handle_stop().await,
            "restart" => self.handle_restart().await,
            "pause" => self.handle_pause().await,
            "resume" => self.handle_resume().await,
            "status" => self.handle_status().await,
            "config" => self.handle_config(&args[1..]).await,
            "help" | "h" | "?" => {
                self.print_help();
                Ok(())
            }
            "exit" | "quit" | "q" => {
                info!("{}", "Goodbye!".green());
                std::process::exit(0);
            }
            _ => {
                info!("{}: Unknown command '{}'", "Error".red(), args[0]);
                info!("Type 'help' for available commands.");
                Ok(())
            }
        }
    }

    /// 处理启动命令
    async fn handle_start(&self) -> Result<()> {
        info!("{}", "Starting data stream...".blue());

        // 检查配置是否已设置
        match ConfigManager::ensure_can_start() {
            Ok(_) => {
                info!("{}", "✓ Configuration validated".green());
            }
            Err(e) => {
                info!("{}: {}", "Error".red(), e);
                info!(
                    "{}",
                    "Please configure the system first using 'config' commands.".yellow()
                );
                return Ok(());
            }
        }

        if let Some(controller) = self.get_data_stream_controller().await {
            match controller.lock().await.start().await {
                Ok(_) => {
                    info!("{}", "✓ Data stream started successfully".green());
                }
                Err(e) => {
                    info!("{}: Failed to start data stream: {}", "Error".red(), e);
                }
            }
        } else {
            info!("{}: Data stream controller not available", "Error".red());
        }

        Ok(())
    }

    /// 处理停止命令
    async fn handle_stop(&self) -> Result<()> {
        info!("{}", "Stopping data stream...".blue());

        if let Some(controller) = self.get_data_stream_controller().await {
            match controller.lock().await.stop().await {
                Ok(_) => {
                    info!("{}", "✓ Data stream stopped successfully".green());
                }
                Err(e) => {
                    info!("{}: Failed to stop data stream: {}", "Error".red(), e);
                }
            }
        } else {
            info!("{}: Data stream controller not available", "Error".red());
        }

        Ok(())
    }

    /// 处理重启命令
    async fn handle_restart(&self) -> Result<()> {
        info!("{}", "Restarting data stream...".blue());
        self.handle_stop().await?;
        self.handle_start().await?;
        Ok(())
    }

    /// 处理暂停命令
    async fn handle_pause(&self) -> Result<()> {
        info!("{}", "Pausing data stream...".blue());

        if let Some(controller) = self.get_data_stream_controller().await {
            match controller.lock().await.pause().await {
                Ok(_) => {
                    info!("{}", "✓ Data stream paused successfully".green());
                }
                Err(e) => {
                    info!("{}: Failed to pause data stream: {}", "Error".red(), e);
                }
            }
        } else {
            info!("{}: Data stream controller not available", "Error".red());
        }

        Ok(())
    }

    /// 处理恢复命令
    async fn handle_resume(&self) -> Result<()> {
        info!("{}", "Resuming data stream...".blue());

        if let Some(controller) = self.get_data_stream_controller().await {
            match controller.lock().await.resume().await {
                Ok(_) => {
                    info!("{}", "✓ Data stream resumed successfully".green());
                }
                Err(e) => {
                    info!("{}: Failed to resume data stream: {}", "Error".red(), e);
                }
            }
        } else {
            info!("{}: Data stream controller not available", "Error".red());
        }

        Ok(())
    }

    /// 处理状态查询命令
    async fn handle_status(&self) -> Result<()> {
        info!("{}", "Checking system status...".blue());

        // 显示框架状态
        let framework_status = self.framework.get_status();
        info!("\n{}", "Framework Status:".bold());
        info!(
            "  Components Initialized: {}",
            if framework_status.components_initialized {
                "✓".green()
            } else {
                "✗".red()
            }
        );
        info!(
            "  Active Tasks: {}",
            framework_status.active_tasks.to_string().cyan()
        );
        info!(
            "  Message Bus: {}",
            if framework_status.message_bus_active {
                "Active".green()
            } else {
                "Inactive".red()
            }
        );

        // 显示数据流状态
        if let Some(controller) = self.get_data_stream_controller().await {
            let ctl = controller.lock().await;
            let status = ctl.get_status().await;
            let config = ctl.get_config().await;
            let stats = controller.lock().await.get_stats().await;

            info!("\n{}", "Data Stream Status:".bold());
            let status_str = match status {
                DataStreamStatus::Stopped => "Stopped".red(),
                DataStreamStatus::Running => "Running".green(),
                DataStreamStatus::Paused => "Paused".yellow(),
                DataStreamStatus::Error(ref e) => format!("Error: {}", e).red(),
            };
            info!("  Status: {}", status_str);
            info!(
                "  Read Interval: {}ms",
                config.read_interval_ms.to_string().cyan()
            );
            info!(
                "  Realtime Simulation: {}",
                if config.realtime_simulation {
                    "Enabled".green()
                } else {
                    "Disabled".red()
                }
            );
            info!("  Buffer Size: {}", config.buffer_size.to_string().cyan());

            info!("\n{}", "Statistics:".bold());
            info!(
                "  Messages Processed: {}",
                stats.messages_processed.to_string().cyan()
            );
            info!("  Error Count: {}", stats.error_count.to_string().cyan());
            if let Some(start_time) = stats.start_time {
                info!(
                    "  Start Time: {}",
                    start_time
                        .format("%Y-%m-%d %H:%M:%S UTC")
                        .to_string()
                        .cyan()
                );
            }
            if let Some(last_time) = stats.last_processed_time {
                info!(
                    "  Last Processed: {}",
                    last_time.format("%Y-%m-%d %H:%M:%S UTC").to_string().cyan()
                );
            }
        } else {
            info!(
                "\n{}: Data stream controller not available",
                "Warning".yellow()
            );
        }

        Ok(())
    }

    /// 处理配置命令
    async fn handle_config(&self, args: &[&str]) -> Result<()> {
        if args.is_empty() {
            info!("{}: Missing config action", "Error".red());
            info!("Available actions: show, set, validate, reset, load, save");
            return Ok(());
        }

        match args[0].to_lowercase().as_str() {
            "show" => self.handle_config_show().await,
            "set" => {
                if args.len() < 2 {
                    info!("{}: Usage: config set <path>", "Error".red());
                    return Ok(());
                }
                self.handle_config_set(args[1]).await
            }
            "validate" => self.handle_config_validate().await,
            "reset" => self.handle_config_reset().await,
            "load" => {
                if args.len() < 2 {
                    info!("{}: Usage: config load <path>", "Error".red());
                    return Ok(());
                }
                self.handle_config_load(args[1]).await
            }
            "save" => {
                if args.len() < 2 {
                    info!("{}: Usage: config save <path>", "Error".red());
                    return Ok(());
                }
                self.handle_config_save(args[1]).await
            }
            _ => {
                info!("{}: Unknown config action '{}'", "Error".red(), args[0]);
                info!("Available actions: show, set, validate, reset, load, save");
                Ok(())
            }
        }
    }

    /// 获取数据流控制器
    async fn get_data_stream_controller(&self) -> Option<Arc<Mutex<DataStreamController>>> {
        self.framework.data_stream_controller()
    }

    /// 打印欢迎信息
    fn print_welcome(&self) {
        info!("{}", "=".repeat(60).blue());
        info!(
            "{}",
            "    Backtest Framework Interactive CLI".bold().green()
        );
        info!("{}", "=".repeat(60).blue());
    }

    /// 打印帮助信息
    fn print_help(&self) {
        info!("{}", "Available Commands:".bold());
        info!("  {}  - Start the data stream", "start".green());
        info!("  {}   - Stop the data stream", "stop".green());
        info!("  {} - Restart the data stream", "restart".green());
        info!("  {}  - Pause the data stream", "pause".green());
        info!("  {} - Resume the data stream", "resume".green());
        info!("  {} - Show current status", "status".green());
        info!("  {} - Configuration management", "config".green());
        info!("    {} - Show current config", "config show".cyan());
        info!("    {} - Set config from file", "config set <path>".cyan());
        info!("    {} - Validate config", "config validate".cyan());
        info!("    {} - Reset config", "config reset".cyan());
        info!(
            "    {} - Load config from file",
            "config load <path>".cyan()
        );
        info!("    {} - Save config to file", "config save <path>".cyan());
        info!("  {}   - Show this help message", "help".green());
        info!("  {}   - Exit the program", "exit".green());
        info!("{}: Use Ctrl+C or Ctrl+D to exit", "Note".yellow());
    }

    /// 显示当前配置
    async fn handle_config_show(&self) -> Result<()> {
        info!("{}", "Current Configuration:".bold());

        match ConfigManager::get() {
            Ok(config) => {
                info!("  Exchange: {}", format!("{:?}", config.exchange).cyan());
                info!(
                    "  Start Time: {}",
                    config
                        .start_time
                        .format("%Y-%m-%d %H:%M:%S UTC")
                        .to_string()
                        .cyan()
                );
                info!(
                    "  End Time: {}",
                    config
                        .end_time
                        .format("%Y-%m-%d %H:%M:%S UTC")
                        .to_string()
                        .cyan()
                );
                info!(
                    "  Data Root Path: {}",
                    config.data_paths.root.display().to_string().cyan()
                );
                info!(
                    "  WebSocket Port: {}",
                    config.websocket_port.to_string().cyan()
                );
                info!("  HTTP Port: {}", config.http_port.to_string().cyan());
                info!("  Log Level: {}", config.log_level.cyan());
                info!(
                    "  Performance Target (μs): {}",
                    config.performance_target_us.to_string().cyan()
                );
            }
            Err(e) => {
                info!("{}: Failed to get config: {}", "Error".red(), e);
                return Ok(());
            }
        }

        // 显示配置状态
        match ConfigManager::get_status() {
            Ok(status) => {
                let status_str = match status {
                    crate::config::ConfigStatus::NotConfigured => "Not Configured".red(),
                    crate::config::ConfigStatus::Configured => "Configured".yellow(),
                    crate::config::ConfigStatus::Validated => "Validated".green(),
                };
                info!("  Status: {}", status_str);
            }
            Err(e) => {
                info!("{}: Failed to get config status: {}", "Error".red(), e);
            }
        }

        match ConfigManager::is_user_configured() {
            Ok(is_user_configured) => {
                info!(
                    "  User Configured: {}",
                    if is_user_configured {
                        "Yes".green()
                    } else {
                        "No".red()
                    }
                );
            }
            Err(e) => {
                info!(
                    "{}: Failed to check user configuration: {}",
                    "Error".red(),
                    e
                );
            }
        }

        match ConfigManager::can_start() {
            Ok(can_start) => {
                info!(
                    "  Can Start: {}",
                    if can_start { "Yes".green() } else { "No".red() }
                );
            }
            Err(e) => {
                info!("{}: Failed to check start capability: {}", "Error".red(), e);
            }
        }

        Ok(())
    }

    /// 设置配置（从文件加载）
    async fn handle_config_set(&self, path: &str) -> Result<()> {
        info!(
            "{}",
            format!("Setting configuration from file: {}", path).blue()
        );

        let path_buf = std::path::PathBuf::from(path);
        match ConfigManager::load_from_file(&path_buf) {
            Ok(_) => {
                info!("{}", "✓ Configuration set successfully".green());
                info!(
                    "{}",
                    "Note: Configuration loaded and marked as user-configured".cyan()
                );
            }
            Err(e) => {
                info!("{}: Failed to set config: {}", "Error".red(), e);
            }
        }

        Ok(())
    }

    /// 验证配置
    async fn handle_config_validate(&self) -> Result<()> {
        info!("{}", "Validating configuration...".blue());

        match ConfigManager::validate() {
            Ok(_) => {
                info!("{}", "✓ Configuration is valid".green());
            }
            Err(e) => {
                info!("{}: Configuration validation failed: {}", "Error".red(), e);
            }
        }

        Ok(())
    }

    /// 重置配置
    async fn handle_config_reset(&self) -> Result<()> {
        info!("{}", "Resetting configuration to defaults...".blue());

        match ConfigManager::reset() {
            Ok(_) => {
                info!("{}", "✓ Configuration reset successfully".green());
                info!(
                    "{}",
                    "Note: Configuration is now in 'Not Configured' state".yellow()
                );
            }
            Err(e) => {
                info!("{}: Failed to reset config: {}", "Error".red(), e);
            }
        }

        Ok(())
    }

    /// 从文件加载配置
    async fn handle_config_load(&self, path: &str) -> Result<()> {
        info!("{}", format!("Loading configuration from: {}", path).blue());

        let path_buf = std::path::PathBuf::from(path);
        match ConfigManager::load_from_file(&path_buf) {
            Ok(_) => {
                info!("{}", "✓ Configuration loaded successfully".green());
            }
            Err(e) => {
                info!("{}: Failed to load config: {}", "Error".red(), e);
            }
        }

        Ok(())
    }

    /// 保存配置到文件
    async fn handle_config_save(&self, path: &str) -> Result<()> {
        info!("{}", format!("Saving configuration to: {}", path).blue());

        let path_buf = std::path::PathBuf::from(path);
        match ConfigManager::save_to_file(&path_buf) {
            Ok(_) => {
                info!("{}", "✓ Configuration saved successfully".green());
            }
            Err(e) => {
                info!("{}: Failed to save config: {}", "Error".red(), e);
            }
        }

        Ok(())
    }
}
